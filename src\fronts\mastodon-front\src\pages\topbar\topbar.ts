import {Component, Vue} from "vue-facing-decorator";
import {TopBarComponent} from "@groupk/vue3-interface-sdk";
import {AutoWired} from "@groupk/horizon2-core";
import SidebarStateListener from "../../../../../shared/utils/SidebarStateListener";

@Component({
	components: {
		"top-bar": TopBarComponent,
	},
})
export default class TopBarView extends Vue {
	@AutoWired(SidebarStateListener) accessor sidebarStateListener!: SidebarStateListener;

	showMenu() {
		this.sidebarStateListener.setOpenedSidebar(true);
	}
}
