<script lang="ts" src="./TicketDesignExposedAttributesFormComponent.ts">
</script>

<style lang="sass">
@use './TicketDesignExposedAttributesFormComponent.scss' as *
</style>

<template>
    <div class="ticket-design-exposed-attributes-form-component form">
        <div class="input-group" v-for="attribute of ticketDesignExposedAttributes" :class="{red: haveInputError(attribute.key)}">
            <label v-if="attribute.type !== TicketDesignExposedAttributeType.CHECKBOX">
                {{ attribute.name }}
                <template v-if="attribute.required"> * </template>
            </label>
            <template v-if="haveInputError(attribute.key)"> {{ haveInputError(attribute.key) }} </template>
            <input
                v-if="attribute.type === TicketDesignExposedAttributeType.STRING"
                :placeholder="attribute.placeholder || ''"
                v-model="getTicketAttribute(attribute.key).value"
            />

            <input
                v-if="attribute.type === TicketDesignExposedAttributeType.NUMBER"
                :placeholder="attribute.placeholder || ''"
                type="number"
                v-model.number="getTicketAttribute(attribute.key).value"
            />

            <dropdown
                v-if="attribute.type === TicketDesignExposedAttributeType.STRING_ALLOWLIST || attribute.type === TicketDesignExposedAttributeType.NUMBER_ALLOWLIST"
                placeholder="Choisissez une valeur"
                :values="attribute.items"
                @update="getTicketAttribute(attribute.key).value = [$event];"
            ></dropdown>

            <div
                v-if="attribute.type === TicketDesignExposedAttributeType.CHECKBOX"
                class="input-group checkbox-group"
                @click="getTicketAttribute(attribute.key).value = !getTicketAttribute(attribute.key).value; forceUpdate()"
            >
                <div class="checkbox" :class="{selected: getTicketAttribute(attribute.key).value}">
                    <i v-if="getTicketAttribute(attribute.key).value" class="fa-regular fa-check"></i>
                </div>
                <div class="checkbox-data">
                    <label class="title"> {{ attribute.name }} </label>
                    <label> {{ attribute.description }} </label>
                </div>
            </div>
        </div>
    </div>
</template>