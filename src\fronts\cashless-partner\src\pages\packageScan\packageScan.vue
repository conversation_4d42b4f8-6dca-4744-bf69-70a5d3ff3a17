<script lang="ts" src="./packageScan.ts"/>

<style lang="sass" scoped>
@import './packageScan.scss'
</style>

<template>
    <div id="package-scan-page">
        <div class="loading-container" v-if="loading">
            <div class="loader"></div>
        </div>
        <div class="container" v-else>

            <div class="actions">
                <div class="action-group">
                    <div class="action">
                        <i class="fa-regular fa-plus"></i>
                    </div>
                    <span class="text"> Nouveau colis </span>
                </div>
            </div>

            <h3> Colis préparés </h3>

            <div class="packages">
                <div class="package" v-for="packageData of existingPackages" @click="selectedPackage = packageData">
                    <span class="name"> {{ packageData.fields.name }} </span>
                </div>
            </div>
        </div>

        <package v-if="selectedPackage" :package-data="selectedPackage" @close="selectedPackage = null"></package>
    </div>
</template>