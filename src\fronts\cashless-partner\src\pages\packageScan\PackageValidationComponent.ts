import {Component, Prop, Vue} from "vue-facing-decorator";
import {AutoWired} from "@groupk/horizon2-core";
import {AirtableRecord} from "./packageScan";
import {BarcodeExternalNative, BarcodeExternalNative_QRCodeReaderReturn, VibratorNative} from "@groupk/native-bridge";

@Component({
    components: {},
    emits: ['close']
})
export default class PackageValidationComponent extends Vue {
    @Prop({required: true}) packageData!: AirtableRecord;
    @Prop({required: true}) objectsInPackage!: AirtableRecord[];

    scannedObjects: AirtableRecord[] = [];

    notIn: AirtableRecord|null = null;
    scanState: 'ERROR'|'SUCCESS'|null = null;

    @AutoWired(BarcodeExternalNative) accessor barcodeExternalNative!: BarcodeExternalNative;
    @AutoWired(VibratorNative) accessor vibratorNative!: VibratorNative;

    async mounted() {
        this.barcodeExternalNative.on('allRead', this.barCodeRead);
    }

    unmounted() {
        this.barcodeExternalNative.off('allRead', this.barCodeRead);
    }

    get notScannedObjects() {
        return this.objectsInPackage.filter((object) => !this.scannedObjects.includes(object));
    }

    async barCodeRead(data: BarcodeExternalNative_QRCodeReaderReturn) {
        const correspondingAirtableObject = this.isIn(data.content);
        if(correspondingAirtableObject === null) {
            this.notIn = correspondingAirtableObject;
            this.scanState = 'ERROR';
        }
        else {
            this.scannedObjects.push(correspondingAirtableObject);
            this.scanState = 'SUCCESS';
        }

        setTimeout(() => this.scanState = null, 200);
    }

    isIn(objectId: string): AirtableRecord|null {
        return this.objectsInPackage.find((object) => object.fields.code.text === objectId) ?? null;
    }

    close() {
        this.$emit('close');
    }
}