import {Component, Prop, Vue} from "vue-facing-decorator";
import {AutoWired, FetchError, genericFetch} from "@groupk/horizon2-core";
import {AirtableRecord} from "./packageScan";
import {BarcodeExternalNative, BarcodeExternalNative_QRCodeReaderReturn, VibratorNative} from "@groupk/native-bridge";
import PackageValidationComponent from "./PackageValidationComponent.vue";

@Component({
    components: {
        'package-validation': PackageValidationComponent
    },
    emits: ['close']
})
export default class PackageComponent extends Vue {
    @Prop({required: true}) packageData!: AirtableRecord;

    objectsInPackage: AirtableRecord[] = [];

    deleting: boolean = false;
    scanning: boolean = false;
    showValidation: boolean = false;
    alreadyIn: AirtableRecord|null = null;
    loading: boolean = true;

    @AutoWired(BarcodeExternalNative) accessor barcodeExternalNative!: BarcodeExternalNative;
    @AutoWired(VibratorNative) accessor vibratorNative!: VibratorNative;

    async mounted() {
        const response = await genericFetch({
            url: 'https://api.airtable.com/v0/app9UWPLidwgXu3jB/objects?maxRecords=80&view=Grid%20view',
            method: 'GET',
            headers: {
                'Authorization': 'Bearer pat5zDgVSdUwXg2IJ.3c32972e7593e4dd64c45b24b65d57a07ddb30ccb2c56a2d7fc97bc247af9515'
            }
        });

        if(response instanceof FetchError) {
        } else {
            const json = await response.json();
            this.objectsInPackage = json.records;
        }

        this.setupListener();

        this.loading = false;
    }

    unmounted() {
        this.barcodeExternalNative.off('allRead', this.barCodeRead);
    }

    setupListener() {
        this.barcodeExternalNative.on('allRead', this.barCodeRead);

    }

    toggleShowValidation() {
        this.barcodeExternalNative.off('allRead', this.barCodeRead);
        this.showValidation = !this.showValidation;
    }

    async barCodeRead(data: BarcodeExternalNative_QRCodeReaderReturn) {
        console.log('here');
        if(this.alreadyIn) {
            this.vibratorNative.vibrate(1000).catch(() => {});
        }

        this.scanning = true;
        await this.addObjectToPackage(data.content, 4);
    }

    async addObjectToPackage(objectId: string, packageId: number) {

        if(this.isAlreadyIn(objectId)) {
            this.alreadyIn = this.isAlreadyIn(objectId);
            this.scanning = false;
            return;
        }

        const response = await genericFetch({
            url: 'https://api.airtable.com/v0/app9UWPLidwgXu3jB/objects',
            method: 'POST',
            headers: {
                'Authorization': 'Bearer pat5zDgVSdUwXg2IJ.3c32972e7593e4dd64c45b24b65d57a07ddb30ccb2c56a2d7fc97bc247af9515',
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                "records": [
                    {
                        "fields": {
                            "code": {
                                "text": objectId
                            },
                            "packageId": packageId
                        }
                    }
                ]
            })
        });

        if(response instanceof FetchError) {
        } else {
            const json = await response.json();
            this.objectsInPackage.push(json.records[0]);
        }

        this.scanning = false;
    }

    async deleteObjectFromPackage(airtableId: string) {
        this.deleting = true;

        const response = await genericFetch({
            url: 'https://api.airtable.com/v0/app9UWPLidwgXu3jB/objects?records[]=' + airtableId,
            method: 'DELETE',
            headers: {
                'Authorization': 'Bearer pat5zDgVSdUwXg2IJ.3c32972e7593e4dd64c45b24b65d57a07ddb30ccb2c56a2d7fc97bc247af9515',
                'Content-Type': 'application/json'
            }
        });

        if(response instanceof FetchError) {
        } else {
            const index = this.objectsInPackage.findIndex((object) => object.id === airtableId);
            if(index !== -1) {
                this.objectsInPackage.splice(index, 1);
            }
        }

        this.alreadyIn = null;
        this.deleting = false;
    }

    isAlreadyIn(objectId: string): AirtableRecord|null {
        return this.objectsInPackage.find((object) => object.fields.code.text === objectId) ?? null;
    }

    close() {
        this.$emit('close');
    }
}