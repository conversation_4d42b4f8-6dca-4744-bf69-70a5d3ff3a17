import {Component, Vue} from "vue-facing-decorator";
import {AutoWired, genericFetch} from "@groupk/horizon2-core";
import {FetchError} from "@groupk/horizon2-core";
import SidebarStateListener from "../../../../../shared/utils/SidebarStateListener";
import PackageComponent from "./PackageComponent.vue";

export interface AirtableRecord {
    id: string;
    fields: any;
    createdTime: string;
}

@Component({
    components: {
        'package': PackageComponent
    }
})
export default class packageScan extends Vue {
    existingPackages: AirtableRecord[] = [];

    selectedPackage: AirtableRecord|null = null;

    loading: boolean = true;

    @AutoWired(SidebarStateListener) accessor sidebarStateListener!: SidebarStateListener;

    beforeMount() {
        this.sidebarStateListener.setHiddenSidebar(true);
        this.sidebarStateListener.setHiddenTopBar(true);
    }

    async mounted() {
        const response = await genericFetch({
            url: 'https://api.airtable.com/v0/app9UWPLidwgXu3jB/package?maxRecords=80&view=Grid%20view',
            method: 'GET',
            headers: {
                'Authorization': 'Bearer pat5zDgVSdUwXg2IJ.3c32972e7593e4dd64c45b24b65d57a07ddb30ccb2c56a2d7fc97bc247af9515'
            }
        });

        if(response instanceof FetchError) {
        } else {
            const json = await response.json();
            this.existingPackages = json.records;
        }

        this.loading = false;
    }
}