import {Component, Vue} from "vue-facing-decorator";
import {
	DropdownComponent,
	FilterTableLayoutComponent,
} from "@groupk/vue3-interface-sdk";
import {ContentHeaderParameters, TableColumn} from "@groupk/vue3-interface-sdk";
import {
	AutoWired, EnumUtils, ScopedUuid, SearchUtils, VisualScopedUuid,
} from "@groupk/horizon2-core";
import {TableColumnsRepository} from "../../../../../shared/repositories/TableColumnsRepository";
import {Router} from "@groupk/horizon2-front";
import {EstablishmentRepository} from "../../../../../shared/repositories/EstablishmentRepository";
import {
	EstablishmentApiOut,
	EstablishmentDeviceAppV2ApiOut,
	EstablishmentDeviceV2ApiOut,
	UuidScopeEstablishment
} from "@groupk/mastodon-core";
import SidebarStateListener from "../../../../../shared/utils/SidebarStateListener";
import {AppState} from "../../../../../shared/AppState";
import {
	EstablishmentDeviceAppSettingsApi_classes,
	EstablishmentDeviceAppSettingsApi_type, UuidScopeIot_device
} from "../../../../../../../../IdeaProjects/mastodon/core/src";
import {EstablishmentDeviceRepository} from "../../../../../shared/repositories/EstablishmentDeviceRepository";

@Component({
	components: {
		'filter-table-layout': FilterTableLayoutComponent,
		dropdown: DropdownComponent
	}
})
export default class Establishments extends Vue {
	establishments: EstablishmentApiOut[] = [];
	selectedEstablishment: EstablishmentApiOut|null = null;
	establishmentDeviceApps: Record<VisualScopedUuid<UuidScopeEstablishment>, EstablishmentDeviceAppV2ApiOut[]> = {};
	establishmentDevices: EstablishmentDeviceV2ApiOut[] = [];

	headerParameters: ContentHeaderParameters = {
		header: 'Établissements',
		subtitle: 'Liste des établissements de vos clients',
		actions: [],
		hideSearch: false,
		searchPlaceholder: 'Rechercher un établissement'
	}

	allowedFilters = {};

	tableKey = 'mastodon-establishments';
	tableColumns: TableColumn[] = [{
		title: 'Identifiant', name: 'uid', displayed: false, mobileHidden: true
	}, {
		title: 'Nom', name: 'name', displayed: true, mobileHidden: false
	}];

	searchString: string = '';
	sort: {
		name: string,
		direction: 'asc'|'desc'
	}|null = null;
	availableSorts: string[] = [];
	applicationFilter: string = 'Tout';

	filteredEstablishments: EstablishmentApiOut[] = [];
	loading: boolean = false;

	@AutoWired(EstablishmentRepository) accessor establishmentRepository!: EstablishmentRepository
	@AutoWired(EstablishmentDeviceRepository) accessor establishmentDeviceRepository!: EstablishmentDeviceRepository
	@AutoWired(TableColumnsRepository) accessor tableColumnsRepository!: TableColumnsRepository
	@AutoWired(SidebarStateListener) accessor sidebarStateListener!: SidebarStateListener
	@AutoWired(AppState) accessor appState!: AppState
	@AutoWired(Router) accessor router!: Router

	beforeMount() {
		this.loading = true;

		this.sidebarStateListener.setHiddenSidebar(false);
		this.sidebarStateListener.setMinimizedSidebar(false);

		let savedPreferences = this.tableColumnsRepository.getColumnPreferences(this.tableKey, this.tableColumns);
		if(savedPreferences) this.tableColumns = savedPreferences;
	}

	async mounted() {
		this.establishments = (await this.establishmentRepository.callContract('list', undefined, {})).success();

		for(const establishment of this.establishments) {
			const data = (await this.establishmentDeviceRepository.callContract('list', {establishmentUid: establishment.uid}, undefined)).success();
			this.establishmentDeviceApps[establishment.uid] = data.appList;
			this.establishmentDevices.push(...data.list);

			for(let iotDeviceType of EstablishmentDeviceAppSettingsApi_classes) {
				// Extract word between EstablishmentDevice and SettingsApi in iotDeviceType.name
				const name = iotDeviceType.name.match(/EstablishmentDevice(.*)SettingsApi/)?.[1];
				if(!name) continue;
				const index = this.tableColumns.findIndex((column) => column.name === name);
				if(index === -1) this.tableColumns.push({
					title: name,
					name: name,
					displayed: true,
					mobileHidden: false
				});
			}

			for(let backApplication of establishment.backApplications) {
				const index = this.tableColumns.findIndex((column) => column.name === backApplication);
				if(index === -1) this.tableColumns.push({
					title: backApplication,
					name: backApplication,
					displayed: true,
					mobileHidden: false
				});

				if(!this.availableSorts.includes(backApplication)) this.availableSorts.push(backApplication);
			}

			for(let frontApplication of establishment.frontApplications) {
				const index = this.tableColumns.findIndex((column) => column.name === frontApplication);
				if(index === -1) this.tableColumns.push({
					title: frontApplication,
					name: frontApplication,
					displayed: true,
					mobileHidden: false
				});
				if(!this.availableSorts.includes(frontApplication)) this.availableSorts.push(frontApplication);
			}
		}

		this.filteredEstablishments = this.establishments;
		this.loading = false;
	}

	getDeviceNumberForEstablishmentWithType(establishment: EstablishmentApiOut, type: string) {
		let total: number = 0;
		for(const deviceApp of this.establishmentDeviceApps[establishment.uid]) {
			if(deviceApp.settings.constructor.name.includes(type)) total++;
		}
		return total;
	}

	getDeviceAppsBySettingsType(establishment: EstablishmentApiOut): Record<string, EstablishmentDeviceAppV2ApiOut[]> {
		const deviceAppsBySettingsType: Record<string, EstablishmentDeviceAppV2ApiOut[]> = {};
		for(const deviceApp of this.establishmentDeviceApps[establishment.uid]) {
			const type = deviceApp.settings.constructor.name.replace('EstablishmentDevice', '').replace('SettingsApi', '');
			if(!deviceAppsBySettingsType[type]) deviceAppsBySettingsType[type] = [];
			deviceAppsBySettingsType[type].push(deviceApp);
		}
		return deviceAppsBySettingsType;
	}

	getSettingsTypeTranslation(type: string) {
		if(type === 'Cashless') return 'Cashless';
		if(type === 'PointOfSale') return 'Caisse';
		if(type === 'EscposPrinter') return 'Imprimante';
		if(type === 'Shelly') return 'Shelly';
		if(type === 'Generic') return 'Générique';
		return type;
	}

	toggleSelectedEstablishment(establishment: EstablishmentApiOut) {
		if(this.selectedEstablishment && this.selectedEstablishment.uid === establishment.uid) {
			this.selectedEstablishment = null;
		} else {
			this.selectedEstablishment = establishment;
		}
	}

	get applicationDropdownValues() {
		const applications: string[] = ['Tout'];
		for(let establishment of this.establishments) {
			for(let application of establishment.backApplications) {
				if(!applications.includes(application)) applications.push(application);
			}
			for(let application of establishment.frontApplications) {
				if(!applications.includes(application)) applications.push(application);
			}
		}
		return applications.map((application) => {
			return {
				name: application,
				value: application
			}
		});
	}

	saveColumnPreferences(columns: TableColumn[]) {
		this.tableColumnsRepository.saveColumnsPreferences(this.tableKey, columns);
	}

	search(value: string) {
		this.searchString = value;
		this.filter();
	}

	filter() {
		this.filteredEstablishments = SearchUtils.searchInTab(this.establishments, (establishment) => {
			return [establishment.name];
		}, this.searchString);
		this.filteredEstablishments = this.filteredEstablishments.filter((establishment) => {
			if(this.applicationFilter === 'Tout') return true;
			return establishment.frontApplications.includes(this.applicationFilter) || establishment.backApplications.includes(this.applicationFilter)
		});
		if(this.sort) this.filteredEstablishments = this.filteredEstablishments.sort((e1, e2) => {
			if(!this.sort) return 0;
			const directionMultiplier = this.sort.direction === 'asc' ? 1 : -1;

			return (
				(e1.backApplications.includes(this.sort.name) || e1.frontApplications.includes(this.sort.name) ? 1 : 0) -
				(e2.backApplications.includes(this.sort.name) || e2.frontApplications.includes(this.sort.name) ? 1 : 0)
			) * directionMultiplier;
		});
	}

	sorted(sort: {
		name: string,
		direction: 'asc'|'desc'
	}) {
		this.sort = sort;
		this.filter();
	}

	requireDeviceWithUid(uid: ScopedUuid<UuidScopeIot_device>) {
		const devices = this.establishmentDevices.filter((device) => device.uid === uid);
		if(devices.length === 0) throw new Error('missing_device');
		return devices[0];
	}
}