import {Component, Prop, Vue} from "vue-facing-decorator";
import {FormModalOrDrawerComponent} from "@groupk/vue3-interface-sdk";
import {ProductRevisionNotDetailedApiOut} from "@groupk/mastodon-core";

@Component({
    components: {
        'form-modal-or-drawer': FormModalOrDrawerComponent
    },
    emits: ['created', 'close']
})
export default class BillingAccountFormComponent extends Vue {
    @Prop({required: true}) productRevision!: ProductRevisionNotDetailedApiOut;

    customValue: string = '';
    opened: boolean = false;
    loading: boolean = false;
    error: string|null = null;

    mounted() {
        setTimeout(() => this.opened = true, 0);
    }

    close() {
        this.opened = false;
        setTimeout(() => this.$emit('close'), 300);
    }

    create() {
        if (!this.customValue.trim()) {
            this.error = "Le compte de classe ne peut pas être vide";
            return;
        }

        this.error = null;
        this.loading = true;

        // Emit the created event with the billing account value
        this.$emit('created', this.customValue.trim());
        
        this.loading = false;
        this.close();
    }
}
