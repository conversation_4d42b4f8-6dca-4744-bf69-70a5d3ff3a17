import {EventApiIn, EventApiOut, EventLocationApi} from "@groupk/mastodon-core";
import {EventLocationGeoApi, UuidScopeProductProduct} from "@groupk/mastodon-core";
import {ScopedUuid, VisualScopedUuid} from "@groupk/horizon2-core";
import {UuidScopeMastodon_upload} from "@groupk/mastodon-core";
import {
	UuidScopeProduct_stockSimple,
	UuidScopeProduct_stockTemporal
} from "@groupk/mastodon-core";

export class EventLocationData {
	line1: string = '';
	line2: string = '';
	city: string = '';
	postalCode: string = '';
	countryIso3: string = '';
	geo: EventLocationGeoApi|null = null;

	constructor(eventLocationApi: EventLocationApi|null = null) {
		if(eventLocationApi) {
			this.line1 = eventLocationApi.line1;
			this.line2 = eventLocationApi.line2 ?? '';
			this.city = eventLocationApi.city;
			this.postalCode = eventLocationApi.postalCode;
			this.countryIso3 = eventLocationApi.countryIso3;
			this.geo = eventLocationApi.geo;
		}
	}

	toApi() {
		return new EventLocationApi({
			name: undefined,
			line1: this.line1,
			line2: this.line2 || null,
			city: this.city,
			postalCode: this.postalCode,
			countryIso3: this.countryIso3,
			geo: this.geo
		});
	}
}

export class EventData {
	name: string = '';
	startingDate: string = '';
	startingTime: string = '';
	endingDate: string = '';
	endingTime: string = '';
	description: string = '';
	location: EventLocationData|null = null;
	productUids: VisualScopedUuid<UuidScopeProductProduct>[] = [];
	mainImageUploadUid: VisualScopedUuid<UuidScopeMastodon_upload>|null = null;
	indicativeGlobalStockUidList: ScopedUuid<UuidScopeProduct_stockSimple|UuidScopeProduct_stockTemporal>[] = [];

	constructor(eventApiOut: EventApiOut|null = null) {
		if(eventApiOut) {
			this.name = eventApiOut.name;
			this.startingDate = eventApiOut.startingDate ?? '';
			this.startingTime = eventApiOut.startingTime ? eventApiOut.startingTime.slice(0, -3) : '';
			this.endingDate = eventApiOut.endingDate ?? '';
			this.endingTime = eventApiOut.endingTime ? eventApiOut.endingTime.slice(0, -3) : '';
			this.description = eventApiOut.description;
			this.productUids = eventApiOut.productUids;
			this.mainImageUploadUid = eventApiOut.mainImageUploadUid;
			this.indicativeGlobalStockUidList = eventApiOut.indicativeGlobalStockUidList;

			if(eventApiOut.location) {
				this.location = new EventLocationData(eventApiOut.location);
			}
		}
	}

	toApi() {
		return new EventApiIn({
			name: this.name,
			startingDate: this.startingDate || null,
			startingTime: this.startingTime ? this.startingTime + ':00' : null,
			endingDate: this.endingDate || null,
			endingTime: this.endingTime ? this.endingTime + ':59' : null,
			description: this.description,
			location: this.location ? this.location.toApi() : null,
			productUids: this.productUids,
			mainImageUploadUid: this.mainImageUploadUid,
			indicativeGlobalStockUidList: this.indicativeGlobalStockUidList
		});
	}
}